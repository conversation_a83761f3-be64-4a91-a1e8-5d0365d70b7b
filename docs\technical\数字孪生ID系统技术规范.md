@ -0,0 +1,580 @@
# 数字孪生ID系统技术规范

## 📋 文档信息
- **版本**：v3.0
- **更新日期**：2025-07-20
- **适用范围**：数字孪生ID统一主控器系统
- **状态**：正式发布

## 🎯 技术规范概述

本文档定义了数字孪生ID统一主控器系统的技术规范，包括统一接口标准、模块化架构、策略切换机制、数据格式、性能要求和质量标准。

## 🏗️ 系统架构规范

### 统一主控器架构
```
DigitalTwinController (统一主控器)
├── CardSizeActivationController (卡牌尺寸启动控制器) 🆕
├── ProcessingStrategy (策略选择层)
│   ├── PHASE1_BASIC (基础功能策略)
│   ├── PHASE2_COMPLETE (完整功能策略)
│   └── CUSTOM (自定义策略)
├── Phase1Integrator (第一阶段集成器)
├── Phase2Integrator (第二阶段集成器)
└── 7个专业化功能模块
    ├── DataValidator (数据验证器)
    ├── SimpleInheritor (简单继承器)
    ├── RegionTransitioner (区域流转器)
    ├── DarkCardProcessor (暗牌处理器)(处理物理ID的两种不同显示方式)
    ├── BasicIDAssigner (基础ID分配器)(明暗牌处理器)
    ├── OcclusionCompensator (遮挡补偿器)
    └── CardSizeActivationController (卡牌尺寸启动控制器) 🆕
```

### 设计原则
1. **统一入口**：所有数字孪生功能通过主控器访问
2. **策略切换**：支持运行时策略切换，适应不同场景
3. **模块解耦**：各功能模块独立，通过标准接口通信
4. **配置集中**：统一的配置管理和性能监控

## 📊 数据格式规范

### 输入数据格式
```python
# 检测结果标准格式
DetectionInput = {
    'label': str,           # 牌面标签，如：'二', '三', '四', '暗'
    'bbox': List[float],    # 边界框 [x1, y1, x2, y2]
    'confidence': float,    # 置信度 [0.0, 1.0]
    'group_id': int        # 区域ID，标识卡牌所在区域
}

# 输入数据列表
detections: List[DetectionInput]
```

### 输出数据格式
```python
@dataclass
class DigitalTwinCard:
    """数字孪生卡牌标准数据结构"""
    # 基础属性
    label: str              # 牌面标签
    bbox: List[float]       # 边界框
    confidence: float       # 置信度
    group_id: int          # 区域ID
    
    # 数字孪生属性
    twin_id: str           # 数字孪生ID（如：1二、2二、虚拟二）
    is_virtual: bool       # 是否为虚拟牌
    is_dark: bool          # 是否为暗牌
    sequence_number: int   # 序号 [1, 2, 3, 4]
    
    # 状态属性
    inherited: bool        # 是否继承自前一帧
    region_transitioned: bool  # 是否发生区域流转
    compensated: bool      # 是否为补偿卡牌
    
    # 元数据
    frame_id: Optional[str]    # 帧ID
    timestamp: Optional[float] # 时间戳
```

### 处理结果格式
```python
@dataclass
class ProcessingResult:
    """处理结果标准结构"""
    processed_cards: List[DigitalTwinCard]  # 处理后的卡牌列表
    statistics: Dict[str, Any]              # 统计信息
    warnings: List[str]                     # 警告信息
    frame_info: Dict[str, Any]              # 帧信息
    
    # 统计信息结构
    statistics = {
        'total_cards': int,           # 总卡牌数
        'inherited_cards': int,       # 继承卡牌数
        'new_cards': int,            # 新卡牌数
        'virtual_cards': int,        # 虚拟卡牌数
        'dark_cards': int,           # 暗牌数
        'compensated_cards': int,    # 补偿卡牌数
        'inheritance_rate': float,   # 继承率
        'processing_time': float     # 处理时间（毫秒）
    }
```

## 🔧 统一主控器接口规范

### 主控器核心接口
```python
class DigitalTwinController:
    """数字孪生统一主控器接口"""

    def process_frame(self,
                     detections: List[Dict[str, Any]],
                     strategy: Optional[ProcessingStrategy] = None) -> ProcessingResult:
        """
        处理单帧数据 - 统一入口

        Args:
            detections: 检测结果列表
            strategy: 处理策略，如果为None则使用默认策略

        Returns:
            处理结果
        """
        pass

    def switch_strategy(self, new_strategy: ProcessingStrategy) -> None:
        """
        切换处理策略

        Args:
            new_strategy: 新的处理策略
        """
        pass

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态信息"""
        pass

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        pass

    def get_available_strategies(self) -> List[str]:
        """获取可用的处理策略列表"""
        pass
```

### 工厂函数接口
```python
def create_digital_twin_controller(config: Optional[DigitalTwinConfig] = None) -> DigitalTwinController:
    """创建数字孪生主控器的工厂函数"""
    pass

def create_default_controller() -> DigitalTwinController:
    """创建使用默认配置的数字孪生主控器"""
    pass

def create_basic_controller() -> DigitalTwinController:
    """创建使用基础功能的数字孪生主控器"""
    pass

def create_complete_controller() -> DigitalTwinController:
    """创建使用完整功能的数字孪生主控器"""
    pass
```

### 配置接口
```python
@dataclass
class DigitalTwinConfig:
    """数字孪生系统配置"""
    strategy: ProcessingStrategy = ProcessingStrategy.PHASE2_COMPLETE
    enable_logging: bool = True
    log_level: str = "INFO"
    performance_monitoring: bool = True

    # 模块开关
    enable_inheritance: bool = True
    enable_region_transition: bool = True
    enable_dark_card_processing: bool = True
    enable_occlusion_compensation: bool = True

    # 性能配置
    max_cards_per_frame: int = 50
    max_virtual_cards: int = 20

    # 输出配置
    dual_output_enabled: bool = True
    preserve_original_data: bool = True
```

### 处理结果接口
```python
@dataclass
class ProcessingResult:
    """处理结果统一格式"""
    success: bool
    processed_cards: List[Dict[str, Any]]
    statistics: Dict[str, Any]
    validation_errors: Optional[List[str]] = None
    validation_warnings: Optional[List[str]] = None
    processing_time: float = 0.0
    strategy_used: str = ""
```

## 🔧 传统接口规范（兼容性）

### 核心接口定义
```python
class DigitalTwinProcessor(ABC):
    """数字孪生处理器接口（传统接口，保持兼容性）"""

    @abstractmethod
    def process_frame(self, detections: List[Dict]) -> ProcessingResult:
        """处理单帧数据"""
        pass

    @abstractmethod
    def reset_system(self) -> None:
        """重置系统状态"""
        pass
    
    @abstractmethod
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        pass
```

### 模块接口标准
```python
class ModuleInterface(ABC):
    """模块接口标准"""
    
    @abstractmethod
    def process(self, data: Any) -> Any:
        """处理数据"""
        pass
    
    @abstractmethod
    def reset(self) -> None:
        """重置模块状态"""
        pass
    
    @abstractmethod
    def get_status(self) -> Dict[str, Any]:
        """获取模块状态"""
        pass
```

## 📏 ID分配规范

### ID格式标准
```python
# 物理ID格式：{序号}{牌面}
PHYSICAL_ID_FORMAT = r"^[1-4][二三四五六七八九十壹]$"
# 示例：1二, 2三, 3四, 4五

# 虚拟ID格式：虚拟{牌面}
VIRTUAL_ID_FORMAT = r"^虚拟[二三四五六七八九十壹]$"
# 示例：虚拟二, 虚拟三

# 暗牌ID格式：{序号}{牌面}暗
DARK_ID_FORMAT = r"^[1-4][二三四五六七八九十壹]暗$"
# 示例：1二暗, 2三暗
```

### ID分配规则
1. **物理ID优先**：每种牌面最多分配4个物理ID
2. **虚拟ID补充**：超过4张时分配虚拟ID
3. **暗牌关联**：暗牌关联到具体牌面（1暗 → 1二暗）
4. **ID稳定性**：一经分配的ID永不改变

### 计数器管理
```python
# ID计数器结构
id_counters = {
    '二': 2,    # 当前已分配2个物理ID
    '三': 1,    # 当前已分配1个物理ID
    '四': 4,    # 已达到物理ID上限
    # ... 其他牌面
}

# 虚拟ID计数器
virtual_counters = {
    '二': 1,    # 当前有1个虚拟ID
    '四': 2,    # 当前有2个虚拟ID
    # ... 其他牌面
}
```

## 🎯 卡牌尺寸启动控制规范 🆕

### 启动控制接口
```python
class CardSizeActivationController:
    """卡牌尺寸启动控制器接口"""

    def should_activate_digital_twin(self, detections: List[Dict]) -> ActivationDecision:
        """判断是否应该启动数字孪生功能"""
        pass

    def get_statistics(self) -> Dict[str, Any]:
        """获取启动控制统计信息"""
        pass
```

### 启动决策数据结构
```python
@dataclass
class ActivationDecision:
    """启动决策结果"""
    should_activate: bool           # 是否应该启动数字孪生
    qualified_ratio: float          # 尺寸合格率
    card_count: int                # 观战方手牌数量
    reason: str                    # 决策原因
    preserve_data: bool            # 是否保留原始数据
    size_analysis: Optional[Dict] = None     # 尺寸分析详情
    baseline_info: Optional[Dict] = None     # 基准信息
    timestamp: Optional[float] = None        # 决策时间戳
```

### 配置规范
```python
@dataclass
class CardSizeActivationConfig:
    """卡牌尺寸启动控制配置"""
    size_threshold: float = 0.85           # 尺寸阈值
    qualified_ratio_threshold: float = 0.9 # 合格卡牌比例阈值
    min_card_count: int = 20               # 最少卡牌数
    baseline_cache_enabled: bool = True    # 缓存基准数据
    enable_size_logging: bool = True       # 启用尺寸日志
```

### 启动条件规范
1. **卡牌数量要求**：观战方手牌区(group_id=1)卡牌数量 ≥ 20张
2. **尺寸质量要求**：尺寸合格率 ≥ 90%（基于0.85尺寸阈值）
3. **数据有效性**：自动过滤UI元素和无效标签
4. **基准自适应**：从现有JSON文件自动学习尺寸基准

### 处理模式规范
```python
# 启动模式：条件满足时的处理流程
ACTIVATION_MODE = {
    'digital_twin_enabled': True,
    'processing_mode': 'full_pipeline',
    'data_preservation': False,
}

# 保留模式：条件不满足时的处理流程
PRESERVATION_MODE = {
    'digital_twin_enabled': False,
    'processing_mode': 'passthrough',
    'data_preservation': True,
}
```

## ⚡ 性能规范

### 时间复杂度要求
- **单模块处理**：O(n)，n为卡牌数量
- **系统整体处理**：O(n)，线性时间复杂度
- **内存复杂度**：O(k+m)，k为区域-标签组合数，m为牌面类型数

### 性能指标要求
```python
PERFORMANCE_REQUIREMENTS = {
    'processing_time_per_frame': 10,      # 每帧处理时间 < 10ms
    'memory_usage': 50,                   # 内存使用 < 50MB
    'inheritance_rate': 0.90,             # 继承率 > 90%
    'id_assignment_accuracy': 0.95,       # ID分配准确率 > 95%
    'system_availability': 0.999,         # 系统可用性 > 99.9%
}
```

### 质量指标
```python
QUALITY_METRICS = {
    'code_coverage': 0.90,                # 代码覆盖率 > 90%
    'unit_test_pass_rate': 1.0,          # 单元测试通过率 = 100%
    'integration_test_pass_rate': 1.0,    # 集成测试通过率 = 100%
    'documentation_coverage': 0.95,       # 文档覆盖率 > 95%
}
```

## 🔒 安全规范

### 数据验证
```python
# 输入数据验证规则
VALIDATION_RULES = {
    'label': {
        'type': str,
        'allowed_values': ['二', '三', '四', '五', '六', '七', '八', '九', '十', '壹', '暗'],
        'required': True
    },
    'bbox': {
        'type': list,
        'length': 4,
        'element_type': float,
        'required': True
    },
    'confidence': {
        'type': float,
        'range': [0.0, 1.0],
        'required': True
    },
    'group_id': {
        'type': int,
        'range': [1, 10],
        'required': True
    }
}
```

### 错误处理
```python
# 标准错误类型
class DigitalTwinError(Exception):
    """数字孪生系统基础错误"""
    pass

class ValidationError(DigitalTwinError):
    """数据验证错误"""
    pass

class ProcessingError(DigitalTwinError):
    """处理过程错误"""
    pass

class SystemError(DigitalTwinError):
    """系统级错误"""
    pass
```

## 📊 监控规范

### 系统状态监控
```python
# 系统状态结构
system_status = {
    'frame_count': int,           # 处理帧数
    'inheritance_rate': float,    # 继承率
    'processing_speed': float,    # 处理速度（帧/秒）
    'memory_usage': float,        # 内存使用（MB）
    'error_count': int,          # 错误计数
    'warning_count': int,        # 警告计数
    'uptime': float,             # 运行时间（秒）
    'last_reset_time': str,      # 最后重置时间
}
```

### 性能监控
```python
# 性能指标监控
performance_metrics = {
    'avg_processing_time': float,     # 平均处理时间
    'max_processing_time': float,     # 最大处理时间
    'min_processing_time': float,     # 最小处理时间
    'throughput': float,              # 吞吐量（帧/秒）
    'success_rate': float,            # 成功率
    'error_rate': float,              # 错误率
}
```

## 🧪 测试规范

### 单元测试要求
```python
# 测试覆盖率要求
TEST_COVERAGE_REQUIREMENTS = {
    'line_coverage': 0.90,        # 行覆盖率 > 90%
    'branch_coverage': 0.85,      # 分支覆盖率 > 85%
    'function_coverage': 0.95,    # 函数覆盖率 > 95%
}

# 测试用例要求
TEST_CASE_REQUIREMENTS = {
    'normal_cases': True,         # 正常情况测试
    'edge_cases': True,           # 边界情况测试
    'error_cases': True,          # 错误情况测试
    'performance_tests': True,    # 性能测试
}
```

### 集成测试要求
```python
# 集成测试场景
INTEGRATION_TEST_SCENARIOS = [
    'single_card_processing',     # 单卡处理
    'multiple_cards_processing',  # 多卡处理
    'inheritance_scenarios',      # 继承场景
    'region_transition_scenarios', # 区域流转场景
    'dark_card_scenarios',        # 暗牌处理场景
    'occlusion_scenarios',        # 遮挡补偿场景
    'system_reset_scenarios',     # 系统重置场景
    'size_activation_scenarios',  # 卡牌尺寸启动控制场景 🆕
]
```

## 📚 文档规范

### 代码文档要求
```python
# 函数文档标准
def process_frame(self, detections: List[Dict]) -> ProcessingResult:
    """
    处理单帧检测数据
    
    Args:
        detections: 检测结果列表，每个元素包含label, bbox, confidence, group_id
        
    Returns:
        ProcessingResult: 包含处理后的卡牌列表和统计信息
        
    Raises:
        ValidationError: 输入数据验证失败
        ProcessingError: 处理过程中发生错误
        
    Example:
        >>> system = create_phase2_integrator()
        >>> detections = [{'label': '二', 'bbox': [0,0,100,100], 'confidence': 0.9, 'group_id': 1}]
        >>> result = system.process_frame(detections)
        >>> print(len(result.processed_cards))
        1
    """
    pass
```

### API文档要求
- **完整性**：覆盖所有公开接口
- **准确性**：与实际实现保持一致
- **示例性**：提供清晰的使用示例
- **时效性**：及时更新文档内容

## 🔄 版本控制规范

### 版本号规范
- **格式**：MAJOR.MINOR.PATCH
- **MAJOR**：不兼容的API修改
- **MINOR**：向后兼容的功能性新增
- **PATCH**：向后兼容的问题修正

### 变更日志规范
```markdown
## [2.0.0] - 2025-07-21
### Added
- 数字孪生ID模块化系统第二阶段功能
- 区域流转器模块
- 暗牌处理器模块
- 遮挡补偿器模块

### Changed
- 重构系统架构为模块化设计
- 优化ID分配算法

### Removed
- 删除老版本memory_manager依赖
- 清理过时的测试文件
```

## 📝 总结

本技术规范定义了数字孪生ID模块化系统的完整技术标准，包括：

### ✅ 核心规范
1. **数据格式标准**：输入输出数据的标准格式
2. **接口规范**：模块和系统的接口标准
3. **性能要求**：时间复杂度和性能指标要求
4. **质量标准**：代码质量和测试覆盖率要求

### 🎯 规范价值
1. **标准化**：确保系统组件的一致性
2. **可维护性**：提供清晰的维护指南
3. **可扩展性**：为系统扩展提供标准框架
4. **质量保证**：确保系统的高质量交付

遵循本技术规范将确保数字孪生ID系统的高质量、高性能和高可维护性。